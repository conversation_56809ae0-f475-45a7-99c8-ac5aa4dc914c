package com.travelsky.ppp.improved.infrastructure.event;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * ClassName:DomainEvent
 * Package:com.travelsky.ppp.improved.infrastructure.event
 * Description:
 * 领域事件对象
 * @date:2025/6/12 16:58
 * @author:<a href="mailto:<EMAIL>">jtli</a>
 */
@Data
public class DomainEvent<T> {
    private String source;
    private LocalDateTime time;
    private T message;
}
