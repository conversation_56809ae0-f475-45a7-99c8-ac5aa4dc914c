package com.travelsky.ppp.improved.infrastructure.model;

import com.baomidou.mybatisplus.annotation.*;
import lombok.*;
import java.time.LocalDateTime;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("issue_list")
public class IssueList {

    /** 主键 */
    @TableId(type = IdType.AUTO)
    private Long id;

    /** 问题描述 (Markdown) */
    private String description;

    /** 来源 */
    private String source;

    /** 严重程度 */
    private Severity severity;

    /** 优先级 */
    private Priority priority;

    /** 原因分析 (Markdown) */
    private String causeAnalysis;

    /** 解决措施 */
    private String solution;

    /** 责任人 */
    private String owner;

    /** 问题提出时间 */
    private LocalDateTime issueTime;

    /** 计划解决时间 */
    private LocalDateTime planResolveTime;

    /** 实际解决时间 */
    private LocalDateTime actualResolveTime;

    /** 当前状态 */
    private Status status;

    /** 跟踪记录（如改进链接） */
    private String trackingRecord;

    private boolean 


    /* ---------- 枚举定义 ---------- */

    /** 严重程度 */
    public enum Severity {
        @EnumValue SEVERE,     // 严重
        @EnumValue MODERATE,   // 中度
        @EnumValue NORMAL,     // 一般
        @EnumValue MINOR       // 轻微
    }

    /** 优先级 */
    public enum Priority {
        @EnumValue HIGH,
        @EnumValue MEDIUM,
        @EnumValue LOW
    }

    /** 状态 */
    public enum Status {
        @EnumValue TODO,
        @EnumValue IN_PROGRESS,
        @EnumValue READY,
        @EnumValue DONE
    }
}
